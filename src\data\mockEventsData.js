// Mock data for Events page
// This file contains sample data structures for events and temporary card assignments

export const mockEvents = [
  {
    id: 1,
    name: "Annual Company Conference 2024",
    description: "Three-day conference featuring keynote speakers, workshops, and networking sessions for all company employees and partners.",
    startDate: "2024-08-15",
    startTime: "09:00",
    endDate: "2024-08-17",
    endTime: "17:00",
    duration: "3 days",
    status: "active",
    location: "Grand Convention Center",
    maxAttendees: 500,
    currentAttendees: 342,
    createdAt: "2024-07-01T10:30:00Z",
    createdBy: "<PERSON>",
    temporaryCards: [
      {
        id: 101,
        memberId: 1,
        memberName: "<PERSON>",
        memberEmail: "<EMAIL>",
        cardType: "VIP Access",
        cardTypeId: 1,
        assignedAt: "2024-07-15T14:20:00Z",
        validFrom: "2024-08-15T09:00:00Z",
        validUntil: "2024-08-17T17:00:00Z",
        status: "active",
        accessLevel: "full"
      },
      {
        id: 102,
        memberId: 2,
        memberName: "<PERSON>",
        memberEmail: "<EMAIL>",
        cardType: "Standard Access",
        cardTypeId: 2,
        assignedAt: "2024-07-16T09:15:00Z",
        validFrom: "2024-08-15T09:00:00Z",
        validUntil: "2024-08-17T17:00:00Z",
        status: "active",
        accessLevel: "standard"
      }
    ]
  },
  {
    id: 2,
    name: "Product Launch Event",
    description: "Exclusive launch event for our new product line with media, influencers, and key stakeholders.",
    startDate: "2024-09-10",
    startTime: "18:00",
    endDate: "2024-09-10",
    endTime: "22:00",
    duration: "4 hours",
    status: "upcoming",
    location: "Innovation Hub",
    maxAttendees: 150,
    currentAttendees: 89,
    createdAt: "2024-07-20T16:45:00Z",
    createdBy: "Sarah Davis",
    temporaryCards: [
      {
        id: 201,
        memberId: 3,
        memberName: "Carol Brown",
        memberEmail: "<EMAIL>",
        cardType: "Media Pass",
        cardTypeId: 3,
        assignedAt: "2024-07-25T11:30:00Z",
        validFrom: "2024-09-10T17:00:00Z",
        validUntil: "2024-09-10T23:00:00Z",
        status: "pending",
        accessLevel: "media"
      }
    ]
  },
  {
    id: 3,
    name: "Team Building Workshop",
    description: "Interactive workshop focused on team collaboration and communication skills for department heads.",
    startDate: "2024-08-05",
    startTime: "10:00",
    endDate: "2024-08-05",
    endTime: "16:00",
    duration: "6 hours",
    status: "completed",
    location: "Training Center Room A",
    maxAttendees: 25,
    currentAttendees: 23,
    createdAt: "2024-06-15T08:00:00Z",
    createdBy: "Mike Johnson",
    temporaryCards: [
      {
        id: 301,
        memberId: 4,
        memberName: "David Lee",
        memberEmail: "<EMAIL>",
        cardType: "Workshop Access",
        cardTypeId: 4,
        assignedAt: "2024-07-01T13:45:00Z",
        validFrom: "2024-08-05T09:30:00Z",
        validUntil: "2024-08-05T16:30:00Z",
        status: "expired",
        accessLevel: "workshop"
      },
      {
        id: 302,
        memberId: 5,
        memberName: "Emma Wilson",
        memberEmail: "<EMAIL>",
        cardType: "Workshop Access",
        cardTypeId: 4,
        assignedAt: "2024-07-02T10:20:00Z",
        validFrom: "2024-08-05T09:30:00Z",
        validUntil: "2024-08-05T16:30:00Z",
        status: "expired",
        accessLevel: "workshop"
      }
    ]
  },
  {
    id: 4,
    name: "Client Appreciation Dinner",
    description: "Elegant dinner event to appreciate our top clients and strengthen business relationships.",
    startDate: "2024-09-25",
    startTime: "19:00",
    endDate: "2024-09-25",
    endTime: "23:00",
    duration: "4 hours",
    status: "upcoming",
    location: "Luxury Hotel Ballroom",
    maxAttendees: 80,
    currentAttendees: 45,
    createdAt: "2024-07-28T12:00:00Z",
    createdBy: "Lisa Anderson",
    temporaryCards: []
  },
  {
    id: 5,
    name: "Security Training Session",
    description: "Mandatory security awareness training for all employees covering cybersecurity best practices.",
    startDate: "2024-08-20",
    startTime: "14:00",
    endDate: "2024-08-20",
    endTime: "17:00",
    duration: "3 hours",
    status: "active",
    location: "Main Auditorium",
    maxAttendees: 200,
    currentAttendees: 156,
    createdAt: "2024-07-10T09:30:00Z",
    createdBy: "Tom Rodriguez",
    temporaryCards: [
      {
        id: 501,
        memberId: 6,
        memberName: "Jennifer Garcia",
        memberEmail: "<EMAIL>",
        cardType: "Training Access",
        cardTypeId: 5,
        assignedAt: "2024-07-12T15:10:00Z",
        validFrom: "2024-08-20T13:30:00Z",
        validUntil: "2024-08-20T17:30:00Z",
        status: "active",
        accessLevel: "training"
      }
    ]
  }
];

// Mock members data for card assignment
export const mockMembers = [
  {
    id: 1,
    name: "Alice Johnson",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Manager",
    image: null
  },
  {
    id: 2,
    name: "Bob Wilson",
    email: "<EMAIL>",
    department: "Sales",
    role: "Representative",
    image: null
  },
  {
    id: 3,
    name: "Carol Brown",
    email: "<EMAIL>",
    department: "External",
    role: "Media",
    image: null
  },
  {
    id: 4,
    name: "David Lee",
    email: "<EMAIL>",
    department: "Engineering",
    role: "Lead Developer",
    image: null
  },
  {
    id: 5,
    name: "Emma Wilson",
    email: "<EMAIL>",
    department: "HR",
    role: "Specialist",
    image: null
  },
  {
    id: 6,
    name: "Jennifer Garcia",
    email: "<EMAIL>",
    department: "IT",
    role: "Security Analyst",
    image: null
  },
  {
    id: 7,
    name: "Michael Chen",
    email: "<EMAIL>",
    department: "Finance",
    role: "Analyst",
    image: null
  },
  {
    id: 8,
    name: "Sarah Thompson",
    email: "<EMAIL>",
    department: "Operations",
    role: "Coordinator",
    image: null
  }
];

// Mock card types for temporary assignments
export const mockCardTypes = [
  {
    id: 1,
    name: "VIP Access",
    description: "Full access to all event areas and amenities",
    color: "#FFD700",
    permissions: ["full_access", "vip_lounge", "priority_seating"]
  },
  {
    id: 2,
    name: "Standard Access",
    description: "General event access with standard amenities",
    color: "#4A90E2",
    permissions: ["general_access", "standard_seating"]
  },
  {
    id: 3,
    name: "Media Pass",
    description: "Special access for media personnel and press",
    color: "#E94B3C",
    permissions: ["media_area", "interview_access", "photo_rights"]
  },
  {
    id: 4,
    name: "Workshop Access",
    description: "Access to training and workshop sessions",
    color: "#50C878",
    permissions: ["workshop_rooms", "training_materials"]
  },
  {
    id: 5,
    name: "Training Access",
    description: "Access to training sessions and educational content",
    color: "#9B59B6",
    permissions: ["training_rooms", "educational_resources"]
  }
];

// Event status options
export const eventStatuses = [
  { label: "Active", value: "active", color: "#22C55E" },
  { label: "Upcoming", value: "upcoming", color: "#3B82F6" },
  { label: "Completed", value: "completed", color: "#6B7280" },
  { label: "Cancelled", value: "cancelled", color: "#EF4444" },
  { label: "Draft", value: "draft", color: "#F59E0B" }
];

// Card assignment status options
export const cardStatuses = [
  { label: "Active", value: "active", color: "#22C55E" },
  { label: "Pending", value: "pending", color: "#F59E0B" },
  { label: "Expired", value: "expired", color: "#6B7280" },
  { label: "Revoked", value: "revoked", color: "#EF4444" }
];
